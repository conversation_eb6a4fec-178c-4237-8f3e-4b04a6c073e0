import type { LinksFunction } from "@remix-run/cloudflare";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>let, <PERSON><PERSON><PERSON>, ScrollRestoration } from "@remix-run/react";

export const links: LinksFunction = () => [
  { rel: "icon", href: "/favicon.ico", type: "image/x-icon" },
  { rel: "manifest", href: "/manifest.json" },
  { rel: "preconnect", href: "https://fonts.googleapis.com" },
  {
    rel: "preconnect",
    href: "https://fonts.gstatic.com",
    crossOrigin: "anonymous",
  },
  {
    rel: "stylesheet",
    href: "https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap",
  },
];

export function Layout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta
          name="description"
          content="A modern web application built with Remix, Cloudflare, and Neon"
        />
        <meta name="theme-color" content="#000000" />
        <meta property="og:title" content="Remix Cloudflare Neon Starter" />
        <meta
          property="og:description"
          content="A modern web application built with Remix, Cloudflare, and Neon"
        />
        <meta property="og:type" content="website" />
        <meta name="twitter:card" content="summary_large_image" />
        <Meta />
        <Links />
      </head>
      <body>
        {children}
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}

export default function App() {
  return <Outlet />;
}
