@import "tailwindcss";

/* Tailwind 4.0 Theme Configuration */
@theme {
  /* Colors - Optimized for American users with modern, clean palette */
  --color-background: 0 0% 100%;
  --color-foreground: 240 10% 3.9%;
  --color-card: 0 0% 100%;
  --color-card-foreground: 240 10% 3.9%;
  --color-popover: 0 0% 100%;
  --color-popover-foreground: 240 10% 3.9%;
  --color-primary: 240 5.9% 10%;
  --color-primary-foreground: 0 0% 98%;
  --color-secondary: 240 4.8% 95.9%;
  --color-secondary-foreground: 240 5.9% 10%;
  --color-muted: 240 4.8% 95.9%;
  --color-muted-foreground: 240 3.8% 46.1%;
  --color-accent: 240 4.8% 95.9%;
  --color-accent-foreground: 240 5.9% 10%;
  --color-destructive: 0 84.2% 60.2%;
  --color-destructive-foreground: 0 0% 98%;
  --color-border: 240 5.9% 90%;
  --color-input: 240 5.9% 90%;
  --color-ring: 240 5.9% 10%;

  /* Dark mode colors */
  --color-dark-background: 240 10% 3.9%;
  --color-dark-foreground: 0 0% 98%;
  --color-dark-card: 240 10% 3.9%;
  --color-dark-card-foreground: 0 0% 98%;
  --color-dark-popover: 240 10% 3.9%;
  --color-dark-popover-foreground: 0 0% 98%;
  --color-dark-primary: 0 0% 98%;
  --color-dark-primary-foreground: 240 5.9% 10%;
  --color-dark-secondary: 240 3.7% 15.9%;
  --color-dark-secondary-foreground: 0 0% 98%;
  --color-dark-muted: 240 3.7% 15.9%;
  --color-dark-muted-foreground: 240 5% 64.9%;
  --color-dark-accent: 240 3.7% 15.9%;
  --color-dark-accent-foreground: 0 0% 98%;
  --color-dark-destructive: 0 62.8% 30.6%;
  --color-dark-destructive-foreground: 0 0% 98%;
  --color-dark-border: 240 3.7% 15.9%;
  --color-dark-input: 240 3.7% 15.9%;
  --color-dark-ring: 240 4.9% 83.9%;

  /* Border radius - Compact design preference */
  --radius: 0.375rem;

  /* Typography - Inter font for American users */
  --font-sans: "Inter", ui-sans-serif, system-ui, sans-serif;
  --font-mono: ui-monospace, "SF Mono", "Cascadia Code", "Roboto Mono", monospace;
}

/* Dark mode theme variables */
@media (prefers-color-scheme: dark) {
  @theme {
    --color-background: var(--color-dark-background);
    --color-foreground: var(--color-dark-foreground);
    --color-card: var(--color-dark-card);
    --color-card-foreground: var(--color-dark-card-foreground);
    --color-popover: var(--color-dark-popover);
    --color-popover-foreground: var(--color-dark-popover-foreground);
    --color-primary: var(--color-dark-primary);
    --color-primary-foreground: var(--color-dark-primary-foreground);
    --color-secondary: var(--color-dark-secondary);
    --color-secondary-foreground: var(--color-dark-secondary-foreground);
    --color-muted: var(--color-dark-muted);
    --color-muted-foreground: var(--color-dark-muted-foreground);
    --color-accent: var(--color-dark-accent);
    --color-accent-foreground: var(--color-dark-accent-foreground);
    --color-destructive: var(--color-dark-destructive);
    --color-destructive-foreground: var(--color-dark-destructive-foreground);
    --color-border: var(--color-dark-border);
    --color-input: var(--color-dark-input);
    --color-ring: var(--color-dark-ring);
  }
}

/* Base styles */
@layer base {
  * {
    border-color: hsl(var(--color-border));
  }

  body {
    background-color: hsl(var(--color-background));
    color: hsl(var(--color-foreground));
    font-family: var(--font-sans);
    font-feature-settings:
      "rlig" 1,
      "calt" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Compact design utilities for American users */
@layer utilities {
  .compact-spacing {
    @apply space-y-2;
  }

  .compact-padding {
    @apply p-3;
  }

  .compact-margin {
    @apply m-2;
  }

  .text-balance {
    text-wrap: balance;
  }
}
